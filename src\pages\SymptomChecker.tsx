import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Grid,
  Chip,
  List,
  Alert,
  CircularProgress,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  SelectChangeEvent
} from '@mui/material';
import Layout from '../components/layout/Layout';
import SearchIcon from '@mui/icons-material/Search';
import SickIcon from '@mui/icons-material/Sick';
import WarningIcon from '@mui/icons-material/Warning';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import { useNavigate } from 'react-router-dom';
// Import Firebase service for live data
import { getSymptoms, getDiagnosis } from '../services/firebaseSymptomCheckerService';

// Define types for our data
interface Symptom {
  id: string;
  name: string;
}

interface Condition {
  id: string;
  name: string;
  common_name: string;
  probability: number; // Numeric value for calculations
  probability_display?: string; // String format like "67%"
  probability_percentage?: number; // Percentage as number
  probability_label?: string; // "High", "Medium", "Low"
  triage_level: string;
  description: string;
  treatment?: string;
  risk_factors?: string[];
}

interface DiagnosisResult {
  conditions: Condition[];
  triage: {
    triage_level: string;
    serious: string[];
  };
}

const SymptomChecker = () => {
  const navigate = useNavigate();
  const [availableSymptoms, setAvailableSymptoms] = useState<Symptom[]>([]);
  const [selectedSymptoms, setSelectedSymptoms] = useState<string[]>([]);
  const [selectedSymptomNames, setSelectedSymptomNames] = useState<{[key: string]: string}>({});
  const [results, setResults] = useState<DiagnosisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingSymptoms, setLoadingSymptoms] = useState(true);
  const [error, setError] = useState('');
  const [age, setAge] = useState<number>(30);
  const [sex, setSex] = useState<string>('male');

  // Fetch available symptoms when component mounts
  useEffect(() => {
    const fetchAvailableSymptoms = async () => {
      try {
        setLoadingSymptoms(true);
        const data = await getSymptoms();
        setAvailableSymptoms(data);
      } catch (err) {
        console.error('Error fetching symptoms:', err);
        setError('Failed to load symptoms. Please try again later.');
      } finally {
        setLoadingSymptoms(false);
      }
    };

    fetchAvailableSymptoms();
  }, []);

  const handleSexChange = (event: SelectChangeEvent<string>) => {
    setSex(event.target.value);
  };

  const handleAgeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    if (!isNaN(value) && value > 0 && value < 120) {
      setAge(value);
    }
  };

  const handleAddSymptom = (symptom: Symptom | null) => {
    if (symptom && !selectedSymptoms.includes(symptom.id)) {
      setSelectedSymptoms([...selectedSymptoms, symptom.id]);
      setSelectedSymptomNames({
        ...selectedSymptomNames,
        [symptom.id]: symptom.name
      });
    }
  };

  const handleRemoveSymptom = (symptomId: string) => {
    setSelectedSymptoms(selectedSymptoms.filter(id => id !== symptomId));
    const updatedNames = { ...selectedSymptomNames };
    delete updatedNames[symptomId];
    setSelectedSymptomNames(updatedNames);
  };

  const handleCheckSymptoms = async () => {
    if (selectedSymptoms.length === 0) {
      setError('Please select at least one symptom');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const diagnosisResults = await getDiagnosis(selectedSymptoms, sex, age);
      setResults(diagnosisResults);
    } catch (err) {
      console.error('Error getting diagnosis:', err);
      setError('An error occurred while analyzing your symptoms. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setSelectedSymptoms([]);
    setSelectedSymptomNames({});
    setResults(null);
    setError('');
  };

  // Map triage level to urgency label and color
  const getUrgencyInfo = (triageLevel: string) => {
    switch (triageLevel) {
      case 'emergency':
        return { label: 'Emergency', color: 'error' as const };
      case 'consultation':
        return { label: 'Consultation Needed', color: 'warning' as const };
      case 'self_care':
        return { label: 'Self Care', color: 'success' as const };
      default:
        return { label: 'Unknown', color: 'default' as const };
    }
  };

  // Convert probability to readable format
  const getProbabilityLabel = (probability: number | string) => {
    // Handle both numeric and string probability values
    let numericProbability: number;

    if (typeof probability === 'string') {
      // If it's a string like "67%", extract the number
      const match = probability.match(/(\d+)/);
      numericProbability = match ? parseInt(match[1]) / 100 : 0;
    } else if (typeof probability === 'number') {
      numericProbability = probability;
    } else {
      numericProbability = 0;
    }

    if (numericProbability >= 0.5) return { label: 'High', color: 'primary' };
    if (numericProbability >= 0.25) return { label: 'Medium', color: 'default' };
    return { label: 'Low', color: 'default' };
  };



  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard')}
            sx={{ mb: 2 }}
          >
            Back to Dashboard
          </Button>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Symptom Checker
          </Typography>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            Enter your symptoms to get possible conditions and recommendations.
          </Typography>
        </Box>

        {/* Data Source Info */}
        <Alert
          severity="success"
          sx={{
            mb: 2,
            borderRadius: 2,
            '& .MuiAlert-icon': {
              alignItems: 'center'
            }
          }}
        >
          <Typography variant="body2" fontWeight="medium">
            ✅ Using Live Firebase Database
          </Typography>
          <Typography variant="body2">
            This symptom checker is now powered by live medical data from Firebase with 500+ conditions and 220+ symptoms.
          </Typography>
        </Alert>

        {/* Medical Disclaimer */}
        <Alert
          severity="info"
          sx={{
            mb: 4,
            borderRadius: 2,
            '& .MuiAlert-icon': {
              alignItems: 'center'
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body1" fontWeight="medium">
              This is not a medical diagnosis.
            </Typography>
          </Box>
          <Typography variant="body2">
            The symptom checker is for informational purposes only and is not a qualified medical opinion.
            Always consult with a healthcare professional for proper diagnosis and treatment.
          </Typography>
        </Alert>

        <Paper 
          elevation={0} 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            mb: 4
          }}
        >
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Basic Information
          </Typography>
          
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="sex-label">Sex</InputLabel>
                <Select
                  labelId="sex-label"
                  id="sex-select"
                  value={sex}
                  label="Sex"
                  onChange={handleSexChange}
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Age"
                type="number"
                value={age}
                onChange={handleAgeChange}
                InputProps={{ inputProps: { min: 1, max: 120 } }}
              />
            </Grid>
          </Grid>
          
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            What symptoms are you experiencing?
          </Typography>
          
          {loadingSymptoms ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Autocomplete
              options={availableSymptoms}
              getOptionLabel={(option) => option.name}
              renderInput={(params) => (
                <TextField 
                  {...params} 
                  label="Search symptoms" 
                  variant="outlined"
                  fullWidth
                  sx={{ mb: 2 }}
                />
              )}
              onChange={(_, value) => handleAddSymptom(value)}
              value={null}
              disabled={loading}
            />
          )}
          
          <Box sx={{ mb: 3 }}>
            {selectedSymptoms.length > 0 ? (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {selectedSymptoms.map((symptomId) => (
                  <Chip
                    key={symptomId}
                    label={selectedSymptomNames[symptomId]}
                    onDelete={() => handleRemoveSymptom(symptomId)}
                    color="primary"
                    variant="outlined"
                    sx={{ borderRadius: 2 }}
                  />
                ))}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No symptoms selected. Start typing to search for symptoms.
              </Typography>
            )}
          </Box>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={handleCheckSymptoms}
              disabled={loading || selectedSymptoms.length === 0}
              sx={{ 
                borderRadius: 2,
                px: 3
              }}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : 'Check Symptoms'}
            </Button>
            
            {selectedSymptoms.length > 0 && (
              <Button
                variant="outlined"
                onClick={handleReset}
                disabled={loading}
                sx={{ 
                  borderRadius: 2
                }}
              >
                Reset
              </Button>
            )}
          </Box>
          
          {error && (
            <Alert severity="error" sx={{ mt: 3, borderRadius: 2 }}>
              {error}
            </Alert>
          )}
        </Paper>

        {results && (
          <Paper 
            elevation={0} 
            sx={{ 
              p: 3, 
              borderRadius: 3,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
            }}
          >
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Possible Conditions
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Based on the symptoms you provided, here are some possible conditions. 
              Remember, this is not a diagnosis and you should consult with a healthcare professional.
            </Typography>
            
            {/* Emergency Warning */}
            {results.triage.triage_level === 'emergency' && (
              <Alert 
                severity="error" 
                variant="filled"
                sx={{ mb: 3, borderRadius: 2 }}
              >
                <Typography variant="subtitle1" fontWeight="bold">
                  Seek Medical Attention Immediately
                </Typography>
                <Typography variant="body2">
                  Your symptoms suggest a condition that may require immediate medical attention.
                  {results.triage.serious.length > 0 && (
                    <ul style={{ marginTop: 8, marginBottom: 0 }}>
                      {results.triage.serious.map((message, index) => (
                        <li key={index}>{message}</li>
                      ))}
                    </ul>
                  )}
                </Typography>
              </Alert>
            )}
            
            {/* Consultation Recommendation */}
            {results.triage.triage_level === 'consultation' && (
             <Alert 
                severity="warning" 
                sx={{ 
                  mb: 3, 
                  borderRadius: 2, 
                  alignItems: 'center' // This centers the icon vertically
                }}
              >
                <Typography variant="subtitle1" fontWeight="bold">
                  Medical Consultation Recommended
                </Typography>
                <Typography variant="body2">
                  Based on your symptoms, we recommend consulting with a healthcare professional.
                </Typography>
              </Alert>

            )}
            
            <List>
              {results.conditions.slice(0, 5).map((condition, index) => {
                const urgency = getUrgencyInfo(condition.triage_level || results.triage?.triage_level);
                const probability = getProbabilityLabel(condition.probability);
                
                return (
                  <Accordion 
                    key={condition.id}
                    defaultExpanded={index === 0}
                    sx={{ 
                      mb: 2, 
                      borderRadius: 2, 
                      overflow: 'hidden',
                      '&:before': { display: 'none' },
                      boxShadow: index === 0 ? '0 2px 10px rgba(0,114,255,0.15)' : '0 1px 5px rgba(0,0,0,0.05)',
                      border: index === 0 ? '1px solid rgba(0,114,255,0.3)' : '1px solid rgba(0,0,0,0.08)'
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{ 
                        bgcolor: index === 0 ? 'rgba(0,114,255,0.08)' : 'transparent',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <SickIcon color={index === 0 ? "primary" : "action"} sx={{ mr: 2 }} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {condition.common_name || condition.name}
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                            <Chip 
                              label={`Probability: ${probability.label}`}
                              size="small"
                              color={probability.label === 'High' ? 'primary' : 'default'}
                              sx={{ borderRadius: 2 }}
                            />
                            <Chip 
                              label={`Urgency: ${urgency.label}`}
                              size="small"
                              color={urgency.color}
                              sx={{ borderRadius: 2 }}
                            />
                          </Box>
                        </Box>
                        <Typography
                          variant="h6"
                          color={index === 0 ? "primary" : "text.secondary"}
                          sx={{ ml: 2, display: { xs: 'none', sm: 'block' } }}
                        >
                          {condition.probability_percentage ||
                           (typeof condition.probability === 'number' ? Math.round(condition.probability * 100) :
                            condition.probability_display || '0')}%
                        </Typography>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body1" paragraph>
                        {condition.description}
                      </Typography>
                      
                      {condition.treatment && (
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <MedicalServicesIcon fontSize="small" sx={{ mr: 1 }} color="primary" />
                            <Typography variant="subtitle2" fontWeight="bold">
                              Treatment
                            </Typography>
                          </Box>
                          <Typography variant="body2">
                            {condition.treatment}
                          </Typography>
                        </Box>
                      )}
                      
                      {condition.risk_factors && condition.risk_factors.length > 0 && (
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <ReportProblemIcon fontSize="small" sx={{ mr: 1 }} color="warning" />
                            <Typography variant="subtitle2" fontWeight="bold">
                              Risk Factors
                            </Typography>
                          </Box>
                          <Box component="ul" sx={{ mt: 0, pl: 3 }}>
                            {condition.risk_factors.map((factor, i) => (
                              <Typography component="li" variant="body2" key={i}>
                                {factor}
                              </Typography>
                            ))}
                          </Box>
                        </Box>
                      )}
                    </AccordionDetails>
                  </Accordion>
                );
              })}
            </List>
            
            <Box sx={{ mt: 3, p: 2, bgcolor: 'rgba(255, 152, 0, 0.1)', borderRadius: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <WarningIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="bold">
                  Important Note
                </Typography>
              </Box>
              <Typography variant="body2">
                If you're experiencing severe symptoms such as difficulty breathing, chest pain, 
                severe bleeding, or any other emergency condition, please seek immediate medical attention 
                or call emergency services.
              </Typography>
            </Box>
          </Paper>
        )}
      </Container>
    </Layout>
  );
};

export default SymptomChecker;




