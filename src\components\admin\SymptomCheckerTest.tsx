import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import {
  PlayArrow as TestIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { getSymptoms, getDiagnosis } from '../../services/firebaseSymptomCheckerService';

interface TestResult {
  success: boolean;
  message: string;
  data?: any;
}

const SymptomCheckerTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<{
    symptoms: TestResult | null;
    diagnosis: TestResult | null;
  }>({
    symptoms: null,
    diagnosis: null
  });

  const runTests = async () => {
    setTesting(true);
    setResults({ symptoms: null, diagnosis: null });

    try {
      // Test 1: Load symptoms
      console.log('🧪 Testing symptom loading...');
      const symptoms = await getSymptoms();
      
      const symptomsResult: TestResult = {
        success: symptoms.length > 0,
        message: symptoms.length > 0 
          ? `✅ Successfully loaded ${symptoms.length} symptoms`
          : '❌ No symptoms found in database',
        data: { count: symptoms.length, sample: symptoms.slice(0, 3) }
      };

      setResults(prev => ({ ...prev, symptoms: symptomsResult }));

      // Test 2: Test diagnosis with sample symptoms
      if (symptoms.length > 0) {
        console.log('🧪 Testing diagnosis calculation...');
        
        // Use first few symptoms for testing
        const testSymptoms = symptoms.slice(0, 3).map(s => s.id);
        
        const diagnosis = await getDiagnosis(testSymptoms, 'male', 25);
        
        const diagnosisResult: TestResult = {
          success: diagnosis.conditions.length > 0,
          message: diagnosis.conditions.length > 0
            ? `✅ Successfully calculated diagnosis with ${diagnosis.conditions.length} potential conditions`
            : '❌ No conditions found for test symptoms',
          data: {
            conditionsCount: diagnosis.conditions.length,
            triageLevel: diagnosis.triage.triage_level,
            topConditions: diagnosis.conditions.slice(0, 3)
          }
        };

        setResults(prev => ({ ...prev, diagnosis: diagnosisResult }));
      } else {
        setResults(prev => ({ 
          ...prev, 
          diagnosis: { 
            success: false, 
            message: '❌ Cannot test diagnosis without symptoms data' 
          } 
        }));
      }

    } catch (error: any) {
      console.error('❌ Test failed:', error);
      
      if (!results.symptoms) {
        setResults(prev => ({ 
          ...prev, 
          symptoms: { 
            success: false, 
            message: `❌ Failed to load symptoms: ${error.message}` 
          } 
        }));
      }
      
      if (!results.diagnosis) {
        setResults(prev => ({ 
          ...prev, 
          diagnosis: { 
            success: false, 
            message: `❌ Failed to test diagnosis: ${error.message}` 
          } 
        }));
      }
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <TestIcon color="primary" />
          <Typography variant="h6" fontWeight="bold">
            Symptom Checker Firebase Test
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Test the Firebase integration for the symptom checker service.
        </Typography>

        <Button
          variant="contained"
          startIcon={testing ? <CircularProgress size={16} /> : <TestIcon />}
          onClick={runTests}
          disabled={testing}
          sx={{ mb: 3 }}
        >
          {testing ? 'Running Tests...' : 'Run Firebase Tests'}
        </Button>

        {/* Test Results */}
        {(results.symptoms || results.diagnosis) && (
          <Box>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Test Results:
            </Typography>

            {/* Symptoms Test */}
            {results.symptoms && (
              <Alert 
                severity={results.symptoms.success ? 'success' : 'error'} 
                sx={{ mb: 2 }}
                icon={results.symptoms.success ? <CheckIcon /> : <ErrorIcon />}
              >
                <Typography variant="body2" fontWeight="medium">
                  Symptoms Loading Test
                </Typography>
                <Typography variant="body2">
                  {results.symptoms.message}
                </Typography>
                {results.symptoms.success && results.symptoms.data && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" display="block">
                      Sample symptoms: {results.symptoms.data.sample?.map((s: any) => s.name).join(', ')}
                    </Typography>
                  </Box>
                )}
              </Alert>
            )}

            {/* Diagnosis Test */}
            {results.diagnosis && (
              <Alert 
                severity={results.diagnosis.success ? 'success' : 'error'} 
                sx={{ mb: 2 }}
                icon={results.diagnosis.success ? <CheckIcon /> : <ErrorIcon />}
              >
                <Typography variant="body2" fontWeight="medium">
                  Diagnosis Calculation Test
                </Typography>
                <Typography variant="body2">
                  {results.diagnosis.message}
                </Typography>
                {results.diagnosis.success && results.diagnosis.data && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" display="block">
                      Triage Level: {results.diagnosis.data.triageLevel}
                    </Typography>
                    {results.diagnosis.data.topConditions?.length > 0 && (
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="caption" display="block" gutterBottom>
                          Top Conditions:
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          {results.diagnosis.data.topConditions.map((condition: any, index: number) => (
                            <Chip
                              key={index}
                              label={`${condition.name} (${condition.probability_display})`}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    )}
                  </Box>
                )}
              </Alert>
            )}

            {/* Overall Status */}
            {results.symptoms && results.diagnosis && (
              <Alert 
                severity={results.symptoms.success && results.diagnosis.success ? 'success' : 'warning'}
                sx={{ mt: 2 }}
              >
                <Typography variant="body2" fontWeight="medium">
                  Overall Status
                </Typography>
                <Typography variant="body2">
                  {results.symptoms.success && results.diagnosis.success
                    ? '🎉 All tests passed! Symptom checker is working with Firebase data.'
                    : '⚠️ Some tests failed. Check the Firebase data import status above.'}
                </Typography>
              </Alert>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SymptomCheckerTest;
